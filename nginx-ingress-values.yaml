controller:
  # Production-ready replica count
  replicaCount: 2
  
  # Resource limits and requests for production
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi
  
  # Pod disruption budget for high availability
  podDisruptionBudget:
    enabled: true
    minAvailable: 1
  
  # Anti-affinity to spread pods across nodes
  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
            - key: app.kubernetes.io/name
              operator: In
              values:
              - ingress-nginx
            - key: app.kubernetes.io/component
              operator: In
              values:
              - controller
          topologyKey: kubernetes.io/hostname
  
  # Service configuration for Azure LoadBalancer
  service:
    type: LoadBalancer
    annotations:
      # Azure LoadBalancer annotations for production
      service.beta.kubernetes.io/azure-load-balancer-health-probe-request-path: /healthz
      service.beta.kubernetes.io/azure-load-balancer-health-probe-interval: "5"
      service.beta.kubernetes.io/azure-load-balancer-health-probe-num-of-probe: "2"
      # Optional: Use Standard SKU LoadBalancer
      service.beta.kubernetes.io/azure-load-balancer-sku: "Standard"
      # Optional: Assign static public IP (replace with your IP)
      # service.beta.kubernetes.io/azure-load-balancer-ipv4: "YOUR_STATIC_IP"
    
    # Enable HTTP and HTTPS
    ports:
      http: 80
      https: 443
    
    # Target ports
    targetPorts:
      http: http
      https: https
  
  # Ingress controller configuration
  config:
    # Production security headers
    use-forwarded-headers: "true"
    compute-full-forwarded-for: "true"
    use-proxy-protocol: "false"
    
    # SSL configuration
    ssl-protocols: "TLSv1.2 TLSv1.3"
    ssl-ciphers: "ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384"
    ssl-prefer-server-ciphers: "false"
    
    # Performance tuning
    worker-processes: "auto"
    worker-connections: "16384"
    max-worker-open-files: "65536"
    
    # Rate limiting (adjust as needed)
    rate-limit-rps: "100"
    
    # Client body size (adjust as needed)
    proxy-body-size: "50m"
    client-max-body-size: "50m"
    
    # Timeouts
    proxy-connect-timeout: "5"
    proxy-send-timeout: "60"
    proxy-read-timeout: "60"
    
    # Enable real IP
    enable-real-ip: "true"
    
    # Log format for better observability
    log-format-escape-json: "true"
    log-format-upstream: '{"time": "$time_iso8601", "remote_addr": "$proxy_protocol_addr", "x_forwarded_for": "$proxy_add_x_forwarded_for", "request_id": "$req_id", "remote_user": "$remote_user", "bytes_sent": $bytes_sent, "request_time": $request_time, "status": $status, "vhost": "$host", "request_proto": "$server_protocol", "path": "$uri", "request_query": "$args", "request_length": $request_length, "duration": $request_time,"method": "$request_method", "http_referrer": "$http_referer", "http_user_agent": "$http_user_agent", "upstream_addr": "$upstream_addr", "upstream_response_time": "$upstream_response_time", "upstream_response_length": "$upstream_response_length", "upstream_status": "$upstream_status"}'
  
  # Metrics for monitoring
  metrics:
    enabled: true
    serviceMonitor:
      enabled: false  # Set to true if using Prometheus Operator
    prometheusRule:
      enabled: false  # Set to true if using Prometheus Operator
  
  # Autoscaling (optional)
  autoscaling:
    enabled: false  # Set to true to enable HPA
    minReplicas: 2
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

# Default backend (optional but recommended for production)
defaultBackend:
  enabled: true
  replicaCount: 1
  resources:
    limits:
      cpu: 50m
      memory: 64Mi
    requests:
      cpu: 10m
      memory: 32Mi
