# Namespace for sample applications
apiVersion: v1
kind: Namespace
metadata:
  name: sample-apps
---
# Sample App 1 - Frontend
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-app
  namespace: sample-apps
  labels:
    app: frontend-app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: frontend-app
  template:
    metadata:
      labels:
        app: frontend-app
    spec:
      containers:
      - name: frontend
        image: nginx:1.25-alpine
        ports:
        - containerPort: 80
        resources:
          requests:
            cpu: 50m
            memory: 64Mi
          limits:
            cpu: 100m
            memory: 128Mi
        volumeMounts:
        - name: html
          mountPath: /usr/share/nginx/html
      volumes:
      - name: html
        configMap:
          name: frontend-html
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: frontend-html
  namespace: sample-apps
data:
  index.html: |
    <!DOCTYPE html>
    <html>
    <head>
        <title>Frontend App</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; background: #f0f8ff; }
            h1 { color: #0066cc; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 Frontend Application</h1>
            <p>This is the frontend service running on path <strong>/frontend</strong></p>
            <p>Served via NGINX Ingress Controller with TLS termination</p>
            <hr>
            <p><strong>Service:</strong> frontend-app</p>
            <p><strong>Path:</strong> /frontend/*</p>
        </div>
    </body>
    </html>
---
apiVersion: v1
kind: Service
metadata:
  name: frontend-service
  namespace: sample-apps
spec:
  selector:
    app: frontend-app
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
---
# Sample App 2 - API
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-app
  namespace: sample-apps
  labels:
    app: api-app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: api-app
  template:
    metadata:
      labels:
        app: api-app
    spec:
      containers:
      - name: api
        image: nginx:1.25-alpine
        ports:
        - containerPort: 80
        resources:
          requests:
            cpu: 50m
            memory: 64Mi
          limits:
            cpu: 100m
            memory: 128Mi
        volumeMounts:
        - name: html
          mountPath: /usr/share/nginx/html
      volumes:
      - name: html
        configMap:
          name: api-html
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: api-html
  namespace: sample-apps
data:
  index.html: |
    <!DOCTYPE html>
    <html>
    <head>
        <title>API Service</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; background: #f0fff0; }
            h1 { color: #006600; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>⚡ API Service</h1>
            <p>This is the API service running on path <strong>/api</strong></p>
            <p>Served via NGINX Ingress Controller with TLS termination</p>
            <hr>
            <p><strong>Service:</strong> api-app</p>
            <p><strong>Path:</strong> /api/*</p>
            <p><strong>Endpoint:</strong> GET /api/health</p>
        </div>
    </body>
    </html>
---
apiVersion: v1
kind: Service
metadata:
  name: api-service
  namespace: sample-apps
spec:
  selector:
    app: api-app
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
