#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check NGINX Ingress Controller status
check_nginx_status() {
    print_header "NGINX Ingress Controller Status"
    
    echo "Pods:"
    kubectl get pods -n ingress-nginx -o wide
    echo
    
    echo "Services:"
    kubectl get svc -n ingress-nginx
    echo
    
    echo "ConfigMaps:"
    kubectl get configmap -n ingress-nginx
    echo
    
    # Check if controller is ready
    READY_PODS=$(kubectl get pods -n ingress-nginx -l app.kubernetes.io/component=controller -o jsonpath='{.items[*].status.conditions[?(@.type=="Ready")].status}')
    if [[ "$READY_PODS" == *"False"* ]]; then
        print_error "Some NGINX controller pods are not ready!"
        kubectl describe pods -n ingress-nginx -l app.kubernetes.io/component=controller
    else
        print_status "All NGINX controller pods are ready"
    fi
}

# Function to check cert-manager status
check_certmanager_status() {
    print_header "cert-manager Status"
    
    echo "Pods:"
    kubectl get pods -n cert-manager -o wide
    echo
    
    echo "ClusterIssuers:"
    kubectl get clusterissuer
    echo
    
    echo "Certificates:"
    kubectl get certificate --all-namespaces
    echo
    
    echo "Certificate Requests:"
    kubectl get certificaterequest --all-namespaces
    echo
    
    # Check if cert-manager is ready
    CERT_MANAGER_READY=$(kubectl get pods -n cert-manager -o jsonpath='{.items[*].status.conditions[?(@.type=="Ready")].status}')
    if [[ "$CERT_MANAGER_READY" == *"False"* ]]; then
        print_error "Some cert-manager pods are not ready!"
        kubectl describe pods -n cert-manager
    else
        print_status "All cert-manager pods are ready"
    fi
}

# Function to check ingress resources
check_ingress_resources() {
    print_header "Ingress Resources"
    
    echo "All Ingress resources:"
    kubectl get ingress --all-namespaces -o wide
    echo
    
    echo "Ingress details:"
    kubectl describe ingress --all-namespaces
}

# Function to check TLS certificates
check_tls_certificates() {
    print_header "TLS Certificates Status"
    
    # Get all certificates
    CERTIFICATES=$(kubectl get certificate --all-namespaces -o jsonpath='{range .items[*]}{.metadata.namespace}{" "}{.metadata.name}{"\n"}{end}')
    
    if [ -z "$CERTIFICATES" ]; then
        print_warning "No certificates found"
        return
    fi
    
    while IFS= read -r line; do
        if [ ! -z "$line" ]; then
            NAMESPACE=$(echo $line | cut -d' ' -f1)
            CERT_NAME=$(echo $line | cut -d' ' -f2)
            
            echo "Certificate: $CERT_NAME in namespace: $NAMESPACE"
            kubectl describe certificate $CERT_NAME -n $NAMESPACE
            echo "---"
        fi
    done <<< "$CERTIFICATES"
}

# Function to test ingress connectivity
test_ingress_connectivity() {
    print_header "Testing Ingress Connectivity"
    
    # Get external IP
    EXTERNAL_IP=$(kubectl get svc --namespace ingress-nginx ingress-nginx-controller --template="{{range .status.loadBalancer.ingress}}{{.ip}}{{end}}")
    
    if [ -z "$EXTERNAL_IP" ]; then
        print_error "No external IP found for ingress controller"
        return
    fi
    
    print_status "External IP: $EXTERNAL_IP"
    
    # Test HTTP connectivity
    echo "Testing HTTP connectivity..."
    if curl -s --connect-timeout 5 http://$EXTERNAL_IP > /dev/null; then
        print_status "HTTP connectivity: OK"
    else
        print_error "HTTP connectivity: FAILED"
    fi
    
    # Test HTTPS connectivity (if available)
    echo "Testing HTTPS connectivity..."
    if curl -s --connect-timeout 5 -k https://$EXTERNAL_IP > /dev/null; then
        print_status "HTTPS connectivity: OK"
    else
        print_warning "HTTPS connectivity: FAILED (might be expected if no TLS configured)"
    fi
}

# Function to show logs
show_logs() {
    print_header "Recent Logs"
    
    echo "NGINX Ingress Controller logs (last 50 lines):"
    kubectl logs -n ingress-nginx -l app.kubernetes.io/component=controller --tail=50
    echo
    
    echo "cert-manager logs (last 20 lines):"
    kubectl logs -n cert-manager -l app=cert-manager --tail=20
}

# Function to show resource usage
show_resource_usage() {
    print_header "Resource Usage"
    
    echo "NGINX Ingress Controller resource usage:"
    kubectl top pods -n ingress-nginx 2>/dev/null || print_warning "Metrics server not available"
    echo
    
    echo "cert-manager resource usage:"
    kubectl top pods -n cert-manager 2>/dev/null || print_warning "Metrics server not available"
}

# Main menu
show_menu() {
    echo
    print_header "NGINX Ingress & cert-manager Monitoring Tool"
    echo "1. Check NGINX Ingress Controller status"
    echo "2. Check cert-manager status"
    echo "3. Check Ingress resources"
    echo "4. Check TLS certificates"
    echo "5. Test connectivity"
    echo "6. Show recent logs"
    echo "7. Show resource usage"
    echo "8. Run all checks"
    echo "9. Exit"
    echo
}

# Main execution
if [ "$1" == "--all" ]; then
    check_nginx_status
    check_certmanager_status
    check_ingress_resources
    check_tls_certificates
    test_ingress_connectivity
    show_resource_usage
    exit 0
fi

while true; do
    show_menu
    read -p "Select an option (1-9): " choice
    
    case $choice in
        1) check_nginx_status ;;
        2) check_certmanager_status ;;
        3) check_ingress_resources ;;
        4) check_tls_certificates ;;
        5) test_ingress_connectivity ;;
        6) show_logs ;;
        7) show_resource_usage ;;
        8) 
            check_nginx_status
            check_certmanager_status
            check_ingress_resources
            check_tls_certificates
            test_ingress_connectivity
            show_resource_usage
            ;;
        9) 
            print_status "Goodbye!"
            exit 0
            ;;
        *) 
            print_error "Invalid option. Please select 1-9."
            ;;
    esac
    
    echo
    read -p "Press Enter to continue..."
done
