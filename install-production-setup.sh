#!/bin/bash

set -e

echo "🚀 Installing Production-Ready NGINX Ingress Controller with cert-manager on AKS"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if helm is available
if ! command -v helm &> /dev/null; then
    print_error "helm is not installed or not in PATH"
    exit 1
fi

# Step 1: Install cert-manager
print_status "Step 1: Installing cert-manager..."
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.2/cert-manager.yaml

print_status "Waiting for cert-manager to be ready..."
kubectl wait --for=condition=ready pod -l app=cert-manager -n cert-manager --timeout=300s
kubectl wait --for=condition=ready pod -l app=cainjector -n cert-manager --timeout=300s
kubectl wait --for=condition=ready pod -l app=webhook -n cert-manager --timeout=300s

# Step 2: Add NGINX Ingress Helm repository
print_status "Step 2: Adding NGINX Ingress Helm repository..."
helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
helm repo update

# Step 3: Install NGINX Ingress Controller
print_status "Step 3: Installing NGINX Ingress Controller..."
helm upgrade --install ingress-nginx ingress-nginx/ingress-nginx \
  --namespace ingress-nginx \
  --create-namespace \
  --values nginx-ingress-values.yaml \
  --wait \
  --timeout=600s

# Step 4: Wait for LoadBalancer to get external IP
print_status "Step 4: Waiting for LoadBalancer to get external IP..."
kubectl wait --namespace ingress-nginx \
  --for=condition=ready pod \
  --selector=app.kubernetes.io/component=controller \
  --timeout=300s

# Get the external IP
EXTERNAL_IP=""
while [ -z $EXTERNAL_IP ]; do
  print_status "Waiting for external IP..."
  EXTERNAL_IP=$(kubectl get svc --namespace ingress-nginx ingress-nginx-controller --template="{{range .status.loadBalancer.ingress}}{{.ip}}{{end}}")
  [ -z "$EXTERNAL_IP" ] && sleep 10
done

print_status "External IP assigned: $EXTERNAL_IP"

# Step 5: Apply Let's Encrypt ClusterIssuer
print_warning "Step 5: Please update the email address in letsencrypt-clusterissuer.yaml before applying!"
read -p "Have you updated the email address? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Applying Let's Encrypt ClusterIssuer..."
    kubectl apply -f letsencrypt-clusterissuer.yaml
else
    print_warning "Skipping ClusterIssuer creation. Please update the email and apply manually."
fi

# Step 6: Deploy sample applications
print_status "Step 6: Deploying sample applications..."
kubectl apply -f sample-applications.yaml

# Step 7: Apply production ingress
print_warning "Step 7: Please update the domain name in production-ingress.yaml before applying!"
read -p "Have you updated the domain name? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Applying production ingress..."
    kubectl apply -f production-ingress.yaml
else
    print_warning "Skipping ingress creation. Please update the domain and apply manually."
fi

# Final status check
print_status "Installation completed! Checking status..."
echo
print_status "NGINX Ingress Controller status:"
kubectl get pods -n ingress-nginx
echo
print_status "cert-manager status:"
kubectl get pods -n cert-manager
echo
print_status "Sample applications status:"
kubectl get pods -n sample-apps
echo
print_status "Services:"
kubectl get svc -n ingress-nginx
echo
print_status "Ingress resources:"
kubectl get ingress -n sample-apps

echo
print_status "🎉 Installation completed successfully!"
print_status "External IP: $EXTERNAL_IP"
print_warning "Don't forget to:"
print_warning "1. Point your domain DNS to the external IP: $EXTERNAL_IP"
print_warning "2. Update the email address in letsencrypt-clusterissuer.yaml"
print_warning "3. Update the domain name in production-ingress.yaml"
print_warning "4. Test with staging ClusterIssuer first before using production"
